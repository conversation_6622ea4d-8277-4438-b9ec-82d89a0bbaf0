{"layer": "top", "position": "top", "reload_style_on_change": true, "modules-left": ["custom/launcher", "custom/notification", "clock", "custom/updates", "tray"], "modules-center": ["hyprland/workspaces"], "modules-right": ["group/expand", "pulseaudio", "network", "battery", "custom/power"], "hyprland/workspaces": {"format": "{icon}", "format-icons": {"active": "", "default": "", "empty": ""}, "persistent-workspaces": {"*": [1, 2, 3, 4, 5]}, "on-click": "activate", "on-scroll-up": "hyprctl dispatch workspace e+1", "on-scroll-down": "hyprctl dispatch workspace e-1"}, "custom/launcher": {"format": "", "on-click": "rofi -show drun -theme ~/.config/rofi/launchers/type-1/style-1.rasi", "on-click-right": "rofi -show run", "tooltip": false}, "custom/notification": {"tooltip": false, "format": "", "on-click": "swaync-client -t -sw", "escape": true}, "clock": {"format": "{:%I:%M:%S %p} ", "interval": 1, "tooltip-format": "<tt>{calendar}</tt>", "calendar": {"format": {"today": "<span color='#fAfBfC'><b>{}</b></span>"}}, "actions": {"on-click-right": "shift_down", "on-click": "shift_up"}}, "network": {"format-wifi": "", "format-ethernet": "", "format-disconnected": "", "tooltip-format-disconnected": "Error", "tooltip-format-wifi": "{essid} ({signalStrength}%) ", "tooltip-format-ethernet": "{ifname} 🖧 ", "on-click": "kitty nmtui"}, "pulseaudio": {"format": "{volume}% {icon}", "format-muted": "󰝟", "format-icons": {"headphone": "", "hands-free": "", "headset": "", "phone": "", "portable": "", "car": "", "default": ["", "", ""]}, "on-click": "pavucontrol", "on-click-right": "pactl set-sink-mute @DEFAULT_SINK@ toggle", "on-scroll-up": "pactl set-sink-volume @DEFAULT_SINK@ +2%", "on-scroll-down": "pactl set-sink-volume @DEFAULT_SINK@ -2%"}, "battery": {"interval": 30, "states": {"good": 95, "warning": 30, "critical": 20}, "format": "{capacity}% {icon}", "format-charging": "{capacity}% 󰂄", "format-plugged": "{capacity}% 󰂄 ", "format-alt": "{time} {icon}", "format-icons": ["󰁻", "󰁼", "󰁾", "󰂀", "󰂂", "󰁹"]}, "custom/updates": {"format": "󰅢 {}", "interval": 30, "exec": "dnf check-update --quiet 2>/dev/null | wc -l || echo '0'", "exec-if": "exit 0", "on-click": "kitty sh -c 'sudo dnf upgrade; echo Done - Press enter to exit; read'; pkill -SIGRTMIN+8 waybar", "signal": 8, "tooltip": false}, "custom/expand": {"format": "", "tooltip": false}, "custom/endpoint": {"format": "|", "tooltip": false}, "group/expand": {"orientation": "horizontal", "drawer": {"transition-duration": 600, "transition-to-left": true, "click-to-reveal": true}, "modules": ["custom/expand", "custom/colorpicker", "cpu", "memory", "temperature", "custom/endpoint"]}, "custom/colorpicker": {"format": "{}", "return-type": "json", "interval": "once", "exec": "~/.config/waybar/scripts/colorpicker.sh -j", "on-click": "~/.config/waybar/scripts/colorpicker.sh", "signal": 1}, "cpu": {"format": "󰻠", "tooltip": true}, "memory": {"format": ""}, "temperature": {"critical-threshold": 80, "format": ""}, "tray": {"icon-size": 14, "spacing": 10}}