#!/bin/bash

# Power menu script for Waybar
# Uses rofi to display power options

options="⏻ Shutdown\n⏾ Suspend\n Reboot\n Lock\n Logout"

chosen=$(echo -e "$options" | rofi -dmenu -i -p "Power Menu" -theme-str 'window {width: 200px;}')

case $chosen in
    "⏻ Shutdown")
        systemctl poweroff
        ;;
    "⏾ Suspend")
        systemctl suspend
        ;;
    " Reboot")
        systemctl reboot
        ;;
    " Lock")
        hyprlock
        ;;
    " Logout")
        hyprctl dispatch exit
        ;;
esac
